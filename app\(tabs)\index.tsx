import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Image,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import {
  MapPin,
  Calendar,
  Clock,
  CloudRain,
  Plane,
  Plus,
} from 'lucide-react-native';
import { useRouter } from 'expo-router';

export default function Dashboard() {
  const router = useRouter();

  const upcomingTrip = {
    destination: '东京，日本',
    startDate: '3月15日',
    endDate: '3月22日',
    daysLeft: 12,
    image: 'https://images.pexels.com/photos/2506923/pexels-photo-2506923.jpeg?auto=compress&cs=tinysrgb&w=400',
    alerts: [
      { type: 'weather', message: '3月17日预计有雨' },
      { type: 'flight', message: '航班起飞延误30分钟' },
    ],
  };

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        {/* Header */}
        <View style={styles.header}>
          <View>
            <Text style={styles.greeting}>你好，小雅</Text>
            <Text style={styles.subGreeting}>准备好下一次冒险了吗？</Text>
          </View>
          <Image
            source={{
              uri: 'https://images.pexels.com/photos/774909/pexels-photo-774909.jpeg?auto=compress&cs=tinysrgb&w=100',
            }}
            style={styles.avatar}
          />
        </View>

        {/* Upcoming Trip Card */}
        <TouchableOpacity
          style={styles.upcomingCard}
          onPress={() => router.push('/daily-view')}
          activeOpacity={0.95}>
          <Image source={{ uri: upcomingTrip.image }} style={styles.cardImage} />
          <View style={styles.cardOverlay}>
            <View style={styles.cardContent}>
              <Text style={styles.cardTitle}>即将到来的行程</Text>
              <Text style={styles.destination}>{upcomingTrip.destination}</Text>
              <View style={styles.dateRow}>
                <Calendar size={16} color="#FFFFFF" strokeWidth={2} />
                <Text style={styles.dateText}>
                  {upcomingTrip.startDate} - {upcomingTrip.endDate}
                </Text>
              </View>
              <View style={styles.countdownRow}>
                <Clock size={16} color="#FFFFFF" strokeWidth={2} />
                <Text style={styles.countdownText}>
                  还有 {upcomingTrip.daysLeft} 天
                </Text>
              </View>
            </View>
          </View>
        </TouchableOpacity>

        {/* Smart Alerts */}
        <View style={styles.alertsSection}>
          <Text style={styles.sectionTitle}>智能提醒</Text>
          {upcomingTrip.alerts.map((alert, index) => (
            <View key={index} style={styles.alertCard}>
              <View style={styles.alertIcon}>
                {alert.type === 'weather' ? (
                  <CloudRain size={20} color="#FFC107" strokeWidth={2} />
                ) : (
                  <Plane size={20} color="#4A90E2" strokeWidth={2} />
                )}
              </View>
              <Text style={styles.alertText}>{alert.message}</Text>
            </View>
          ))}
        </View>

        {/* Quick Actions */}
        <View style={styles.quickActions}>
          <Text style={styles.sectionTitle}>快捷操作</Text>
          <TouchableOpacity
            style={styles.actionButton}
            onPress={() => router.push('/ai-planner')}
            activeOpacity={0.9}>
            <Plus size={24} color="#4A90E2" strokeWidth={2} />
            <Text style={styles.actionButtonText}>AI智能规划</Text>
          </TouchableOpacity>
        </View>
      </ScrollView>

      {/* Floating Action Button */}
      <TouchableOpacity
        style={styles.fab}
        onPress={() => router.push('/ai-planner')}
        activeOpacity={0.9}>
        <Plus size={28} color="#FFFFFF" strokeWidth={2} />
      </TouchableOpacity>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F8F9FA',
  },
  scrollView: {
    flex: 1,
    paddingHorizontal: 20,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: 20,
    marginBottom: 30,
  },
  greeting: {
    fontSize: 28,
    fontWeight: '700',
    color: '#212529',
    lineHeight: 34,
  },
  subGreeting: {
    fontSize: 16,
    color: '#6C757D',
    marginTop: 4,
    lineHeight: 20,
  },
  avatar: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: '#E9ECEF',
  },
  upcomingCard: {
    height: 200,
    borderRadius: 16,
    overflow: 'hidden',
    marginBottom: 30,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 12,
    elevation: 4,
  },
  cardImage: {
    width: '100%',
    height: '100%',
    resizeMode: 'cover',
  },
  cardOverlay: {
    ...StyleSheet.absoluteFillObject,
    backgroundColor: 'rgba(0, 0, 0, 0.3)',
    justifyContent: 'flex-end',
  },
  cardContent: {
    padding: 20,
  },
  cardTitle: {
    fontSize: 14,
    color: '#FFFFFF',
    opacity: 0.8,
    marginBottom: 4,
    fontWeight: '500',
  },
  destination: {
    fontSize: 24,
    fontWeight: '700',
    color: '#FFFFFF',
    marginBottom: 12,
    lineHeight: 29,
  },
  dateRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  dateText: {
    fontSize: 16,
    color: '#FFFFFF',
    marginLeft: 8,
    fontWeight: '500',
  },
  countdownRow: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  countdownText: {
    fontSize: 16,
    color: '#FFFFFF',
    marginLeft: 8,
    fontWeight: '500',
  },
  alertsSection: {
    marginBottom: 30,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: '#212529',
    marginBottom: 16,
    lineHeight: 24,
  },
  alertCard: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#FFFFFF',
    padding: 16,
    borderRadius: 12,
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 8,
    elevation: 2,
  },
  alertIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#F8F9FA',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  alertText: {
    flex: 1,
    fontSize: 15,
    color: '#495057',
    lineHeight: 20,
    fontWeight: '500',
  },
  quickActions: {
    marginBottom: 100,
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#FFFFFF',
    padding: 20,
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 8,
    elevation: 2,
  },
  actionButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#4A90E2',
    marginLeft: 12,
  },
  fab: {
    position: 'absolute',
    bottom: 90,
    right: 20,
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: '#4A90E2',
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 12,
    elevation: 8,
  },
});