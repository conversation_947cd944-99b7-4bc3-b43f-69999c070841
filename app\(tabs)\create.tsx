import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TextInput,
  TouchableOpacity,
  Dimensions,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import {
  ArrowLeft,
  ArrowRight,
  MapPin,
  Calendar,
  Mountain,
  Coffee,
  Camera,
  GripVertical,
} from 'lucide-react-native';
import { useRouter } from 'expo-router';

const { width } = Dimensions.get('window');

interface Activity {
  id: string;
  time: string;
  title: string;
  location: string;
  type: 'restaurant' | 'museum' | 'activity';
}

export default function CreateTrip() {
  const router = useRouter();
  const [currentStep, setCurrentStep] = useState(1);
  const [tripData, setTripData] = useState({
    destination: '',
    startDate: '',
    endDate: '',
    preferences: [] as string[],
  });
  const [activities, setActivities] = useState<Activity[]>([
    {
      id: '1',
      time: '09:00',
      title: 'Breakfast at Local Café',
      location: 'Shibuya District',
      type: 'restaurant',
    },
    {
      id: '2',
      time: '11:00',
      title: 'Visit Tokyo National Museum',
      location: 'Ueno Park',
      type: 'museum',
    },
    {
      id: '3',
      time: '14:00',
      title: 'Lunch at Ramen Shop',
      location: 'Shinjuku',
      type: 'restaurant',
    },
    {
      id: '4',
      time: '16:00',
      title: 'Tokyo Skytree Observatory',
      location: 'Sumida',
      type: 'activity',
    },
  ]);

  const preferences = [
    { id: 'leisure', label: 'Leisure', icon: Coffee },
    { id: 'adventure', label: 'Adventure', icon: Mountain },
    { id: 'culture', label: 'Culture', icon: Camera },
  ];

  const togglePreference = (prefId: string) => {
    setTripData(prev => ({
      ...prev,
      preferences: prev.preferences.includes(prefId)
        ? prev.preferences.filter(p => p !== prefId)
        : [...prev.preferences, prefId],
    }));
  };

  const renderStep = () => {
    switch (currentStep) {
      case 1:
        return (
          <View style={styles.stepContent}>
            <Text style={styles.stepTitle}>Where are you going?</Text>
            <Text style={styles.stepSubtitle}>
              输入您的目的地和旅行日期
            </Text>

            <View style={styles.inputGroup}>
              <Text style={styles.inputLabel}>目的地</Text>
              <View style={styles.inputContainer}>
                <MapPin size={20} color="#6C757D" strokeWidth={2} />
                <TextInput
                  style={styles.textInput}
                  placeholder="输入目的地"
                  value={tripData.destination}
                  onChangeText={text =>
                    setTripData(prev => ({ ...prev, destination: text }))
                  }
                  placeholderTextColor="#ADB5BD"
                />
              </View>
            </View>

            <View style={styles.dateRow}>
              <View style={[styles.inputGroup, styles.dateInput]}>
                <Text style={styles.inputLabel}>开始日期</Text>
                <View style={styles.inputContainer}>
                  <Calendar size={20} color="#6C757D" strokeWidth={2} />
                  <TextInput
                    style={styles.textInput}
                    placeholder="3月15日"
                    value={tripData.startDate}
                    onChangeText={text =>
                      setTripData(prev => ({ ...prev, startDate: text }))
                    }
                    placeholderTextColor="#ADB5BD"
                  />
                </View>
              </View>

              <View style={[styles.inputGroup, styles.dateInput]}>
                <Text style={styles.inputLabel}>结束日期</Text>
                <View style={styles.inputContainer}>
                  <Calendar size={20} color="#6C757D" strokeWidth={2} />
                  <TextInput
                    style={styles.textInput}
                    placeholder="Mar 22"
                    value={tripData.endDate}
                    onChangeText={text =>
                      setTripData(prev => ({ ...prev, endDate: text }))
                    }
                    placeholderTextColor="#ADB5BD"
                  />
                </View>
              </View>
            </View>
          </View>
        );

      case 2:
        return (
          <View style={styles.stepContent}>
            <Text style={styles.stepTitle}>What's your travel style?</Text>
            <Text style={styles.stepSubtitle}>
              Select your preferences for personalized recommendations
            </Text>

            <View style={styles.preferencesGrid}>
              {preferences.map(pref => {
                const IconComponent = pref.icon;
                const isSelected = tripData.preferences.includes(pref.id);
                return (
                  <TouchableOpacity
                    key={pref.id}
                    style={[
                      styles.preferenceChip,
                      isSelected && styles.preferenceChipSelected,
                    ]}
                    onPress={() => togglePreference(pref.id)}
                    activeOpacity={0.8}>
                    <IconComponent
                      size={24}
                      color={isSelected ? '#FFFFFF' : '#6C757D'}
                      strokeWidth={2}
                    />
                    <Text
                      style={[
                        styles.preferenceText,
                        isSelected && styles.preferenceTextSelected,
                      ]}>
                      {pref.label}
                    </Text>
                  </TouchableOpacity>
                );
              })}
            </View>
          </View>
        );

      case 3:
        return (
          <View style={styles.stepContent}>
            <Text style={styles.stepTitle}>Plan your day</Text>
            <Text style={styles.stepSubtitle}>
              Drag to reorder activities for March 15
            </Text>

            <ScrollView style={styles.activitiesList} showsVerticalScrollIndicator={false}>
              {activities.map((activity, index) => (
                <View key={activity.id} style={styles.activityItem}>
                  <View style={styles.activityTime}>
                    <Text style={styles.timeText}>{activity.time}</Text>
                  </View>
                  <View style={styles.activityContent}>
                    <View style={styles.activityHeader}>
                      <Text style={styles.activityTitle}>{activity.title}</Text>
                      <GripVertical size={20} color="#ADB5BD" strokeWidth={2} />
                    </View>
                    <Text style={styles.activityLocation}>{activity.location}</Text>
                  </View>
                </View>
              ))}
            </ScrollView>

            <TouchableOpacity style={styles.addActivityButton} activeOpacity={0.8}>
              <Text style={styles.addActivityText}>+ Add Activity</Text>
            </TouchableOpacity>
          </View>
        );

      default:
        return null;
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <View style={styles.navigationHeader}>
        <TouchableOpacity
          onPress={() =>
            currentStep > 1 ? setCurrentStep(currentStep - 1) : router.back()
          }
          style={styles.navButton}
          activeOpacity={0.7}>
          <ArrowLeft size={24} color="#212529" strokeWidth={2} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Create Trip</Text>
        <View style={styles.navButton} />
      </View>

      {/* Progress Indicator */}
      <View style={styles.progressContainer}>
        <View style={styles.progressBar}>
          <View
            style={[
              styles.progressFill,
              { width: `${(currentStep / 3) * 100}%` },
            ]}
          />
        </View>
        <Text style={styles.progressText}>
          Step {currentStep} of 3
        </Text>
      </View>

      {/* Step Content */}
      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {renderStep()}
      </ScrollView>

      {/* Navigation Footer */}
      <View style={styles.footer}>
        {currentStep < 3 ? (
          <TouchableOpacity
            style={styles.nextButton}
            onPress={() => setCurrentStep(currentStep + 1)}
            activeOpacity={0.9}>
            <Text style={styles.nextButtonText}>Continue</Text>
            <ArrowRight size={20} color="#FFFFFF" strokeWidth={2} />
          </TouchableOpacity>
        ) : (
          <TouchableOpacity
            style={styles.nextButton}
            onPress={() => {
              // Handle trip creation
              router.back();
            }}
            activeOpacity={0.9}>
            <Text style={styles.nextButtonText}>Create Trip</Text>
          </TouchableOpacity>
        )}
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  navigationHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#E9ECEF',
  },
  navButton: {
    width: 40,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#212529',
  },
  progressContainer: {
    paddingHorizontal: 20,
    paddingVertical: 20,
  },
  progressBar: {
    height: 4,
    backgroundColor: '#E9ECEF',
    borderRadius: 2,
    marginBottom: 8,
  },
  progressFill: {
    height: '100%',
    backgroundColor: '#4A90E2',
    borderRadius: 2,
  },
  progressText: {
    fontSize: 14,
    color: '#6C757D',
    fontWeight: '500',
  },
  content: {
    flex: 1,
    paddingHorizontal: 20,
  },
  stepContent: {
    paddingBottom: 40,
  },
  stepTitle: {
    fontSize: 28,
    fontWeight: '700',
    color: '#212529',
    marginBottom: 8,
    lineHeight: 34,
  },
  stepSubtitle: {
    fontSize: 16,
    color: '#6C757D',
    marginBottom: 32,
    lineHeight: 20,
  },
  inputGroup: {
    marginBottom: 24,
  },
  inputLabel: {
    fontSize: 16,
    fontWeight: '600',
    color: '#212529',
    marginBottom: 8,
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#F8F9FA',
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 16,
    borderWidth: 1,
    borderColor: '#E9ECEF',
  },
  textInput: {
    flex: 1,
    fontSize: 16,
    color: '#212529',
    marginLeft: 12,
  },
  dateRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  dateInput: {
    flex: 0.48,
  },
  preferencesGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  preferenceChip: {
    width: (width - 60) / 2,
    backgroundColor: '#F8F9FA',
    borderRadius: 12,
    padding: 20,
    alignItems: 'center',
    marginBottom: 16,
    borderWidth: 1,
    borderColor: '#E9ECEF',
  },
  preferenceChipSelected: {
    backgroundColor: '#4A90E2',
    borderColor: '#4A90E2',
  },
  preferenceText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#6C757D',
    marginTop: 8,
  },
  preferenceTextSelected: {
    color: '#FFFFFF',
  },
  activitiesList: {
    flex: 1,
    marginBottom: 20,
  },
  activityItem: {
    flexDirection: 'row',
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 8,
    elevation: 2,
    borderWidth: 1,
    borderColor: '#E9ECEF',
  },
  activityTime: {
    width: 60,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 16,
  },
  timeText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#4A90E2',
  },
  activityContent: {
    flex: 1,
  },
  activityHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 4,
  },
  activityTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#212529',
    flex: 1,
  },
  activityLocation: {
    fontSize: 14,
    color: '#6C757D',
  },
  addActivityButton: {
    backgroundColor: '#F8F9FA',
    borderRadius: 12,
    padding: 20,
    alignItems: 'center',
    borderWidth: 2,
    borderColor: '#E9ECEF',
    borderStyle: 'dashed',
  },
  addActivityText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#4A90E2',
  },
  footer: {
    padding: 20,
    borderTopWidth: 1,
    borderTopColor: '#E9ECEF',
  },
  nextButton: {
    backgroundColor: '#4A90E2',
    borderRadius: 12,
    padding: 18,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  nextButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#FFFFFF',
    marginRight: 8,
  },
});