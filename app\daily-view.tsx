import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Dimensions,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import {
  ArrowLeft,
  Calendar,
  MapPin,
  Clock,
  Coffee,
  Camera,
  Mountain,
  ChevronLeft,
  ChevronRight,
} from 'lucide-react-native';
import { useRouter } from 'expo-router';

const { width } = Dimensions.get('window');

interface Activity {
  id: string;
  time: string;
  title: string;
  location: string;
  type: 'restaurant' | 'museum' | 'activity';
  description?: string;
}

export default function DailyView() {
  const router = useRouter();
  const [selectedDate, setSelectedDate] = useState('March 15');

  const dates = [
    { date: '3月14日', day: '周四' },
    { date: '3月15日', day: '周五' },
    { date: '3月16日', day: '周六' },
    { date: '3月17日', day: '周日' },
    { date: '3月18日', day: '周一' },
  ];

  const activities: Activity[] = [
    {
      id: '1',
      time: '08:00',
      title: '蓝瓶咖啡早餐',
      location: '涩谷区',
      type: 'restaurant',
      description: '用精品咖啡开始美好的一天',
    },
    {
      id: '2',
      time: '10:30',
      title: '东京国立博物馆',
      location: '上野公园',
      type: 'museum',
      description: '探索日本艺术和历史',
    },
    {
      id: '3',
      time: '13:00',
      title: '一兰拉面午餐',
      location: '新宿',
      type: 'restaurant',
      description: '正宗豚骨拉面体验',
    },
    {
      id: '4',
      time: '15:30',
      title: '东京晴空塔观景台',
      location: '墨田区',
      type: 'activity',
      description: '东京全景观赏',
    },
    {
      id: '5',
      time: '18:00',
      title: '数寄屋桥次郎晚餐',
      location: '银座',
      type: 'restaurant',
      description: '世界著名寿司体验',
    },
    {
      id: '6',
      time: '20:30',
      title: '银座夜晚漫步',
      location: '银座区',
      type: 'activity',
      description: '奢华购物和夜生活',
    },
  ];

  const getActivityIcon = (type: string) => {
    switch (type) {
      case 'restaurant':
        return <Coffee size={20} color="#28A745" strokeWidth={2} />;
      case 'museum':
        return <Camera size={20} color="#4A90E2" strokeWidth={2} />;
      case 'activity':
        return <Mountain size={20} color="#FFC107" strokeWidth={2} />;
      default:
        return <MapPin size={20} color="#6C757D" strokeWidth={2} />;
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity
          onPress={() => router.back()}
          style={styles.backButton}
          activeOpacity={0.7}>
          <ArrowLeft size={24} color="#212529" strokeWidth={2} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Tokyo, Japan</Text>
        <Text style={styles.headerTitle}>东京，日本</Text>
        <View style={styles.backButton} />
      </View>

      {/* Date Picker */}
      <View style={styles.datePicker}>
        <ScrollView
          horizontal
          showsHorizontalScrollIndicator={false}
          contentContainerStyle={styles.dateScrollContent}>
          {dates.map((dateItem, index) => (
            <TouchableOpacity
              key={index}
              style={[
                styles.dateChip,
                selectedDate === dateItem.date && styles.dateChipSelected,
              ]}
              onPress={() => setSelectedDate(dateItem.date)}
              activeOpacity={0.8}>
              <Text
                style={[
                  styles.dayText,
                  selectedDate === dateItem.date && styles.dayTextSelected,
                ]}>
                {dateItem.day}
              </Text>
              <Text
                style={[
                  styles.dateText,
                  selectedDate === dateItem.date && styles.dateTextSelected,
                ]}>
                {dateItem.date.split(' ')[1]}
              </Text>
            </TouchableOpacity>
          ))}
        </ScrollView>
      </View>

      {/* Timeline */}
      <ScrollView style={styles.timeline} showsVerticalScrollIndicator={false}>
        <View style={styles.timelineContainer}>
          {activities.map((activity, index) => (
            <View key={activity.id} style={styles.timelineItem}>
              {/* Timeline Line */}
              <View style={styles.timelineLeft}>
                <View style={styles.timelineDot} />
                {index < activities.length - 1 && <View style={styles.timelineLine} />}
              </View>

              {/* Activity Card */}
              <TouchableOpacity
                style={styles.activityCard}
                activeOpacity={0.95}>
                <View style={styles.activityCardHeader}>
                  <View style={styles.activityInfo}>
                    <Text style={styles.activityTime}>{activity.time}</Text>
                    <Text style={styles.activityTitle}>{activity.title}</Text>
                  </View>
                  <View style={styles.activityIcon}>
                    {getActivityIcon(activity.type)}
                  </View>
                </View>
                <View style={styles.locationRow}>
                  <MapPin size={14} color="#6C757D" strokeWidth={2} />
                  <Text style={styles.locationText}>{activity.location}</Text>
                </View>
                {activity.description && (
                  <Text style={styles.descriptionText}>{activity.description}</Text>
                )}
              </TouchableOpacity>
            </View>
          ))}
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F8F9FA',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
    backgroundColor: '#FFFFFF',
    borderBottomWidth: 1,
    borderBottomColor: '#E9ECEF',
  },
  backButton: {
    width: 40,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#212529',
  },
  datePicker: {
    backgroundColor: '#FFFFFF',
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#E9ECEF',
  },
  dateScrollContent: {
    paddingHorizontal: 20,
  },
  dateChip: {
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 16,
    marginRight: 12,
    borderRadius: 12,
    backgroundColor: '#F8F9FA',
    minWidth: 70,
  },
  dateChipSelected: {
    backgroundColor: '#4A90E2',
  },
  dayText: {
    fontSize: 12,
    fontWeight: '500',
    color: '#6C757D',
    marginBottom: 4,
  },
  dayTextSelected: {
    color: '#FFFFFF',
  },
  dateText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#212529',
  },
  dateTextSelected: {
    color: '#FFFFFF',
  },
  timeline: {
    flex: 1,
    paddingHorizontal: 20,
    paddingTop: 20,
  },
  timelineContainer: {
    paddingBottom: 40,
  },
  timelineItem: {
    flexDirection: 'row',
    marginBottom: 20,
  },
  timelineLeft: {
    alignItems: 'center',
    marginRight: 16,
    width: 20,
  },
  timelineDot: {
    width: 12,
    height: 12,
    borderRadius: 6,
    backgroundColor: '#4A90E2',
    marginTop: 6,
  },
  timelineLine: {
    flex: 1,
    width: 2,
    backgroundColor: '#E9ECEF',
    marginTop: 8,
  },
  activityCard: {
    flex: 1,
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 8,
    elevation: 2,
  },
  activityCardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 8,
  },
  activityInfo: {
    flex: 1,
  },
  activityTime: {
    fontSize: 14,
    fontWeight: '600',
    color: '#4A90E2',
    marginBottom: 4,
  },
  activityTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#212529',
    lineHeight: 20,
  },
  activityIcon: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: '#F8F9FA',
    justifyContent: 'center',
    alignItems: 'center',
  },
  locationRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  locationText: {
    fontSize: 14,
    color: '#6C757D',
    marginLeft: 6,
    fontWeight: '500',
  },
  descriptionText: {
    fontSize: 14,
    color: '#495057',
    lineHeight: 18,
  },
});