import React, { useState, useRef, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TextInput,
  TouchableOpacity,
  KeyboardAvoidingView,
  Platform,
  Dimensions,
  Alert,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import {
  Send,
  Bot,
  User,
  Plus,
  Check,
  X,
  GripVertical,
  Calendar,
  MapPin,
  Clock,
  ChevronDown,
  ChevronUp,
} from 'lucide-react-native';

const { width, height } = Dimensions.get('window');

interface Message {
  id: string;
  type: 'user' | 'ai';
  content: string;
  timestamp: Date;
  suggestions?: string[];
}

interface TodoItem {
  id: string;
  title: string;
  description: string;
  time: string;
  location: string;
  completed: boolean;
  date: string;
}

export default function AIPlanner() {
  const [messages, setMessages] = useState<Message[]>([
    {
      id: '1',
      type: 'ai',
      content: '你好！我是Atlas智能行程规划助手。请告诉我您的旅行需求，比如目的地、时间、预算和兴趣爱好，我会为您制定完美的行程计划。',
      timestamp: new Date(),
      suggestions: ['规划东京3日游', '推荐巴黎美食路线', '制定预算旅行计划'],
    },
  ]);
  const [inputText, setInputText] = useState('');
  const [todoItems, setTodoItems] = useState<TodoItem[]>([]);
  const [showTodoPanel, setShowTodoPanel] = useState(false);
  const [isTyping, setIsTyping] = useState(false);
  const scrollViewRef = useRef<ScrollView>(null);

  const sendMessage = async () => {
    if (!inputText.trim()) return;

    const userMessage: Message = {
      id: Date.now().toString(),
      type: 'user',
      content: inputText,
      timestamp: new Date(),
    };

    setMessages(prev => [...prev, userMessage]);
    setInputText('');
    setIsTyping(true);

    // 模拟AI响应
    setTimeout(() => {
      const aiResponse = generateAIResponse(inputText);
      const aiMessage: Message = {
        id: (Date.now() + 1).toString(),
        type: 'ai',
        content: aiResponse.content,
        timestamp: new Date(),
        suggestions: aiResponse.suggestions,
      };

      setMessages(prev => [...prev, aiMessage]);
      setIsTyping(false);

      // 如果AI生成了行程建议，自动添加到todo列表
      if (aiResponse.todoItems) {
        setTodoItems(prev => [...prev, ...aiResponse.todoItems]);
        setShowTodoPanel(true);
      }
    }, 2000);
  };

  const generateAIResponse = (userInput: string) => {
    const input = userInput.toLowerCase();
    
    if (input.includes('东京') || input.includes('日本')) {
      return {
        content: '为您推荐东京3日精品行程：\n\n第一天：传统文化体验\n- 上午：浅草寺参观\n- 下午：银座购物\n- 晚上：新宿美食街\n\n第二天：现代都市探索\n- 上午：东京塔观景\n- 下午：原宿时尚街区\n- 晚上：涩谷夜景\n\n第三天：自然与艺术\n- 上午：上野公园\n- 下午：东京国立博物馆\n- 晚上：台场海滨公园\n\n是否需要我将这些行程添加到您的待办清单中？',
        suggestions: ['添加到行程清单', '调整时间安排', '推荐更多景点'],
        todoItems: [
          {
            id: 'tokyo-1',
            title: '浅草寺参观',
            description: '体验传统日本文化，参观著名的浅草寺',
            time: '09:00',
            location: '浅草寺',
            completed: false,
            date: '2024-03-15',
          },
          {
            id: 'tokyo-2',
            title: '银座购物',
            description: '在高端购物区体验奢华购物',
            time: '14:00',
            location: '银座',
            completed: false,
            date: '2024-03-15',
          },
          {
            id: 'tokyo-3',
            title: '新宿美食街',
            description: '品尝地道日式料理',
            time: '19:00',
            location: '新宿',
            completed: false,
            date: '2024-03-15',
          },
        ],
      };
    } else if (input.includes('巴黎') || input.includes('法国')) {
      return {
        content: '巴黎浪漫之旅为您规划：\n\n经典必游：\n- 埃菲尔铁塔日落观景\n- 卢浮宫艺术之旅\n- 塞纳河游船\n\n美食体验：\n- 香榭丽舍大街咖啡\n- 蒙马特高地法式餐厅\n- 玛黑区精品甜品店\n\n需要我为您生成详细的每日行程安排吗？',
        suggestions: ['生成详细行程', '推荐美食路线', '预算规划建议'],
      };
    } else if (input.includes('预算') || input.includes('便宜')) {
      return {
        content: '为您推荐经济实惠的旅行方案：\n\n住宿建议：\n- 青年旅社或民宿\n- 提前预订享受折扣\n\n交通优化：\n- 使用公共交通\n- 购买城市通票\n\n餐饮策略：\n- 当地市场和街边小食\n- 自助早餐酒店\n\n需要我为特定目的地制定详细的预算计划吗？',
        suggestions: ['东南亚预算游', '欧洲穷游攻略', '国内经济路线'],
      };
    } else {
      return {
        content: '我理解您的需求。为了为您制定最合适的行程，请告诉我：\n\n1. 您想去哪个城市或国家？\n2. 计划出行多少天？\n3. 大概的预算范围？\n4. 您的兴趣爱好（美食、文化、自然风光等）？\n\n有了这些信息，我就能为您量身定制完美的旅行计划！',
        suggestions: ['东京3日游', '欧洲10日游', '国内周末游'],
      };
    }
  };

  const addTodoItem = () => {
    const newItem: TodoItem = {
      id: Date.now().toString(),
      title: '新行程项目',
      description: '点击编辑描述',
      time: '09:00',
      location: '待定',
      completed: false,
      date: new Date().toISOString().split('T')[0],
    };
    setTodoItems(prev => [...prev, newItem]);
  };

  const toggleTodoItem = (id: string) => {
    setTodoItems(prev =>
      prev.map(item =>
        item.id === id ? { ...item, completed: !item.completed } : item
      )
    );
  };

  const deleteTodoItem = (id: string) => {
    Alert.alert(
      '确认删除',
      '确定要删除这个行程项目吗？',
      [
        { text: '取消', style: 'cancel' },
        {
          text: '删除',
          style: 'destructive',
          onPress: () => setTodoItems(prev => prev.filter(item => item.id !== id)),
        },
      ]
    );
  };

  const useSuggestion = (suggestion: string) => {
    setInputText(suggestion);
  };

  useEffect(() => {
    scrollViewRef.current?.scrollToEnd({ animated: true });
  }, [messages]);

  return (
    <SafeAreaView style={styles.container}>
      <KeyboardAvoidingView
        style={styles.keyboardView}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}>
        
        {/* Header */}
        <View style={styles.header}>
          <View style={styles.headerLeft}>
            <Bot size={24} color="#4A90E2" strokeWidth={2} />
            <Text style={styles.headerTitle}>AI智能规划师</Text>
          </View>
          <TouchableOpacity
            onPress={() => setShowTodoPanel(!showTodoPanel)}
            style={styles.todoToggle}
            activeOpacity={0.8}>
            <Text style={styles.todoToggleText}>
              行程清单 ({todoItems.length})
            </Text>
            {showTodoPanel ? (
              <ChevronUp size={16} color="#4A90E2" strokeWidth={2} />
            ) : (
              <ChevronDown size={16} color="#4A90E2" strokeWidth={2} />
            )}
          </TouchableOpacity>
        </View>

        {/* Todo Panel */}
        {showTodoPanel && (
          <View style={styles.todoPanel}>
            <View style={styles.todoPanelHeader}>
              <Text style={styles.todoPanelTitle}>我的行程清单</Text>
              <TouchableOpacity onPress={addTodoItem} style={styles.addTodoButton}>
                <Plus size={16} color="#4A90E2" strokeWidth={2} />
              </TouchableOpacity>
            </View>
            <ScrollView style={styles.todoList} showsVerticalScrollIndicator={false}>
              {todoItems.map(item => (
                <View key={item.id} style={styles.todoItem}>
                  <TouchableOpacity
                    onPress={() => toggleTodoItem(item.id)}
                    style={styles.todoCheckbox}>
                    {item.completed ? (
                      <Check size={16} color="#28A745" strokeWidth={2} />
                    ) : (
                      <View style={styles.uncheckedBox} />
                    )}
                  </TouchableOpacity>
                  <View style={styles.todoContent}>
                    <Text style={[
                      styles.todoTitle,
                      item.completed && styles.todoTitleCompleted
                    ]}>
                      {item.title}
                    </Text>
                    <View style={styles.todoMeta}>
                      <Clock size={12} color="#6C757D" strokeWidth={2} />
                      <Text style={styles.todoTime}>{item.time}</Text>
                      <MapPin size={12} color="#6C757D" strokeWidth={2} />
                      <Text style={styles.todoLocation}>{item.location}</Text>
                    </View>
                  </View>
                  <TouchableOpacity
                    onPress={() => deleteTodoItem(item.id)}
                    style={styles.deleteButton}>
                    <X size={16} color="#DC3545" strokeWidth={2} />
                  </TouchableOpacity>
                </View>
              ))}
              {todoItems.length === 0 && (
                <Text style={styles.emptyTodoText}>
                  暂无行程项目，让AI为您规划吧！
                </Text>
              )}
            </ScrollView>
          </View>
        )}

        {/* Chat Messages */}
        <ScrollView
          ref={scrollViewRef}
          style={styles.chatContainer}
          showsVerticalScrollIndicator={false}
          contentContainerStyle={styles.chatContent}>
          {messages.map(message => (
            <View key={message.id} style={styles.messageContainer}>
              <View style={[
                styles.messageBubble,
                message.type === 'user' ? styles.userMessage : styles.aiMessage
              ]}>
                <View style={styles.messageHeader}>
                  {message.type === 'ai' ? (
                    <Bot size={16} color="#4A90E2" strokeWidth={2} />
                  ) : (
                    <User size={16} color="#FFFFFF" strokeWidth={2} />
                  )}
                  <Text style={[
                    styles.messageAuthor,
                    message.type === 'user' && styles.userMessageAuthor
                  ]}>
                    {message.type === 'ai' ? 'AI助手' : '我'}
                  </Text>
                </View>
                <Text style={[
                  styles.messageText,
                  message.type === 'user' && styles.userMessageText
                ]}>
                  {message.content}
                </Text>
                {message.suggestions && (
                  <View style={styles.suggestionsContainer}>
                    {message.suggestions.map((suggestion, index) => (
                      <TouchableOpacity
                        key={index}
                        style={styles.suggestionChip}
                        onPress={() => useSuggestion(suggestion)}
                        activeOpacity={0.8}>
                        <Text style={styles.suggestionText}>{suggestion}</Text>
                      </TouchableOpacity>
                    ))}
                  </View>
                )}
              </View>
            </View>
          ))}
          
          {isTyping && (
            <View style={styles.messageContainer}>
              <View style={[styles.messageBubble, styles.aiMessage]}>
                <View style={styles.messageHeader}>
                  <Bot size={16} color="#4A90E2" strokeWidth={2} />
                  <Text style={styles.messageAuthor}>AI助手</Text>
                </View>
                <View style={styles.typingIndicator}>
                  <View style={styles.typingDot} />
                  <View style={styles.typingDot} />
                  <View style={styles.typingDot} />
                </View>
              </View>
            </View>
          )}
        </ScrollView>

        {/* Input Area */}
        <View style={styles.inputContainer}>
          <View style={styles.inputWrapper}>
            <TextInput
              style={styles.textInput}
              placeholder="描述您的旅行需求..."
              value={inputText}
              onChangeText={setInputText}
              multiline
              maxLength={500}
              placeholderTextColor="#ADB5BD"
            />
            <TouchableOpacity
              onPress={sendMessage}
              style={[
                styles.sendButton,
                !inputText.trim() && styles.sendButtonDisabled
              ]}
              disabled={!inputText.trim()}
              activeOpacity={0.8}>
              <Send size={20} color="#FFFFFF" strokeWidth={2} />
            </TouchableOpacity>
          </View>
        </View>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F8F9FA',
  },
  keyboardView: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 16,
    backgroundColor: '#FFFFFF',
    borderBottomWidth: 1,
    borderBottomColor: '#E9ECEF',
  },
  headerLeft: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#212529',
    marginLeft: 8,
  },
  todoToggle: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#F8F9FA',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 8,
  },
  todoToggleText: {
    fontSize: 14,
    color: '#4A90E2',
    fontWeight: '500',
    marginRight: 4,
  },
  todoPanel: {
    backgroundColor: '#FFFFFF',
    borderBottomWidth: 1,
    borderBottomColor: '#E9ECEF',
    maxHeight: height * 0.3,
  },
  todoPanelHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#F8F9FA',
  },
  todoPanelTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#212529',
  },
  addTodoButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: '#F8F9FA',
    justifyContent: 'center',
    alignItems: 'center',
  },
  todoList: {
    paddingHorizontal: 20,
    paddingVertical: 8,
  },
  todoItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#F8F9FA',
  },
  todoCheckbox: {
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: '#F8F9FA',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  uncheckedBox: {
    width: 16,
    height: 16,
    borderRadius: 8,
    borderWidth: 2,
    borderColor: '#ADB5BD',
  },
  todoContent: {
    flex: 1,
  },
  todoTitle: {
    fontSize: 15,
    fontWeight: '500',
    color: '#212529',
    marginBottom: 4,
  },
  todoTitleCompleted: {
    textDecorationLine: 'line-through',
    color: '#6C757D',
  },
  todoMeta: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  todoTime: {
    fontSize: 12,
    color: '#6C757D',
    marginLeft: 4,
    marginRight: 8,
  },
  todoLocation: {
    fontSize: 12,
    color: '#6C757D',
    marginLeft: 4,
  },
  deleteButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: '#FFF5F5',
    justifyContent: 'center',
    alignItems: 'center',
  },
  emptyTodoText: {
    textAlign: 'center',
    color: '#ADB5BD',
    fontSize: 14,
    paddingVertical: 20,
  },
  chatContainer: {
    flex: 1,
    paddingHorizontal: 20,
  },
  chatContent: {
    paddingVertical: 20,
  },
  messageContainer: {
    marginBottom: 16,
  },
  messageBubble: {
    maxWidth: width * 0.8,
    padding: 16,
    borderRadius: 16,
  },
  userMessage: {
    backgroundColor: '#4A90E2',
    alignSelf: 'flex-end',
    borderBottomRightRadius: 4,
  },
  aiMessage: {
    backgroundColor: '#FFFFFF',
    alignSelf: 'flex-start',
    borderBottomLeftRadius: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 4,
    elevation: 2,
  },
  messageHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  messageAuthor: {
    fontSize: 12,
    fontWeight: '600',
    color: '#4A90E2',
    marginLeft: 6,
  },
  userMessageAuthor: {
    color: '#FFFFFF',
  },
  messageText: {
    fontSize: 15,
    lineHeight: 20,
    color: '#212529',
  },
  userMessageText: {
    color: '#FFFFFF',
  },
  suggestionsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginTop: 12,
    gap: 8,
  },
  suggestionChip: {
    backgroundColor: '#F8F9FA',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    borderWidth: 1,
    borderColor: '#E9ECEF',
  },
  suggestionText: {
    fontSize: 13,
    color: '#4A90E2',
    fontWeight: '500',
  },
  typingIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  typingDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: '#ADB5BD',
    marginRight: 4,
  },
  inputContainer: {
    backgroundColor: '#FFFFFF',
    borderTopWidth: 1,
    borderTopColor: '#E9ECEF',
    paddingHorizontal: 20,
    paddingVertical: 16,
  },
  inputWrapper: {
    flexDirection: 'row',
    alignItems: 'flex-end',
    backgroundColor: '#F8F9FA',
    borderRadius: 24,
    paddingHorizontal: 16,
    paddingVertical: 8,
    minHeight: 48,
  },
  textInput: {
    flex: 1,
    fontSize: 16,
    color: '#212529',
    maxHeight: 100,
    paddingVertical: 8,
  },
  sendButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: '#4A90E2',
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: 8,
  },
  sendButtonDisabled: {
    backgroundColor: '#ADB5BD',
  },
});