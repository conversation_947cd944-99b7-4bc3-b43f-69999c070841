import React, { useState, useRef, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TextInput,
  TouchableOpacity,
  KeyboardAvoidingView,
  Platform,
  Dimensions,
  Alert,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import {
  Send,
  Bot,
  User,
  Plus,
  Check,
  X,
  GripVertical,
  Calendar,
  MapPin,
  Clock,
  ChevronDown,
  ChevronUp,
} from 'lucide-react-native';

const { width, height } = Dimensions.get('window');

interface Message {
  id: string;
  type: 'user' | 'ai';
  content: string;
  timestamp: Date;
  suggestions?: string[];
}

interface TodoItem {
  id: string;
  title: string;
  description: string;
  time: string;
  location: string;
  completed: boolean;
  date: string;
}

export default function AIPlanner() {
  const [messages, setMessages] = useState<Message[]>([
    {
      id: '1',
      type: 'ai',
      content: '您好，我是 Atlas 智能助手\n\n告诉我您的旅行想法，我将为您规划专属行程\n\n目的地 · 时间 · 预算 · 偏好',
      timestamp: new Date(),
      suggestions: ['东京 3 日', '巴黎美食', '预算旅行'],
    },
  ]);
  const [inputText, setInputText] = useState('');
  const [todoItems, setTodoItems] = useState<TodoItem[]>([]);
  const [showTodoPanel, setShowTodoPanel] = useState(false);
  const [isTyping, setIsTyping] = useState(false);
  const scrollViewRef = useRef<ScrollView>(null);

  const sendMessage = async () => {
    if (!inputText.trim()) return;

    const userMessage: Message = {
      id: Date.now().toString(),
      type: 'user',
      content: inputText,
      timestamp: new Date(),
    };

    setMessages(prev => [...prev, userMessage]);
    setInputText('');
    setIsTyping(true);

    // 模拟AI响应
    setTimeout(() => {
      const aiResponse = generateAIResponse(inputText);
      const aiMessage: Message = {
        id: (Date.now() + 1).toString(),
        type: 'ai',
        content: aiResponse.content,
        timestamp: new Date(),
        suggestions: aiResponse.suggestions,
      };

      setMessages(prev => [...prev, aiMessage]);
      setIsTyping(false);

      // 如果AI生成了行程建议，自动添加到todo列表
      if (aiResponse.todoItems) {
        setTodoItems(prev => [...prev, ...aiResponse.todoItems]);
        setShowTodoPanel(true);
      }
    }, 2000);
  };

  const generateAIResponse = (userInput: string) => {
    const input = userInput.toLowerCase();

    if (input.includes('东京') || input.includes('日本')) {
      return {
        content: '东京 3 日精选行程\n\n第一天 · 传统与现代\n浅草寺 → 银座 → 新宿\n\n第二天 · 都市探索\n东京塔 → 原宿 → 涩谷\n\n第三天 · 艺术自然\n上野公园 → 国立博物馆 → 台场\n\n已为您优化路线和时间安排',
        suggestions: ['加入行程', '调整时间', '更多推荐'],
        todoItems: [
          {
            id: 'tokyo-1',
            title: '浅草寺',
            description: '传统文化体验',
            time: '09:00',
            location: '浅草',
            completed: false,
            date: '2024-03-15',
          },
          {
            id: 'tokyo-2',
            title: '银座购物',
            description: '高端购物体验',
            time: '14:00',
            location: '银座',
            completed: false,
            date: '2024-03-15',
          },
          {
            id: 'tokyo-3',
            title: '新宿美食',
            description: '地道日式料理',
            time: '19:00',
            location: '新宿',
            completed: false,
            date: '2024-03-15',
          },
        ],
      };
    } else if (input.includes('巴黎') || input.includes('法国')) {
      return {
        content: '巴黎经典路线\n\n必游景点\n埃菲尔铁塔 · 卢浮宫 · 塞纳河\n\n美食体验\n香榭丽舍咖啡 · 蒙马特餐厅 · 玛黑甜品\n\n为您规划最佳游览顺序',
        suggestions: ['详细行程', '美食路线', '预算建议'],
      };
    } else if (input.includes('预算') || input.includes('便宜')) {
      return {
        content: '经济旅行方案\n\n住宿优化\n青旅民宿 · 提前预订\n\n交通节省\n公共交通 · 城市通票\n\n餐饮策略\n当地市场 · 街边美食\n\n让旅行更实惠',
        suggestions: ['东南亚', '欧洲穷游', '国内路线'],
      };
    } else {
      return {
        content: '为您定制专属行程\n\n请告诉我：\n· 目的地\n· 出行天数\n· 预算范围\n· 兴趣偏好\n\n我将为您规划完美旅程',
        suggestions: ['东京 3 日', '欧洲 10 日', '周末游'],
      };
    }
  };

  const addTodoItem = () => {
    const newItem: TodoItem = {
      id: Date.now().toString(),
      title: '新行程项目',
      description: '点击编辑描述',
      time: '09:00',
      location: '待定',
      completed: false,
      date: new Date().toISOString().split('T')[0],
    };
    setTodoItems(prev => [...prev, newItem]);
  };

  const toggleTodoItem = (id: string) => {
    setTodoItems(prev =>
      prev.map(item =>
        item.id === id ? { ...item, completed: !item.completed } : item
      )
    );
  };

  const deleteTodoItem = (id: string) => {
    Alert.alert(
      '确认删除',
      '确定要删除这个行程项目吗？',
      [
        { text: '取消', style: 'cancel' },
        {
          text: '删除',
          style: 'destructive',
          onPress: () => setTodoItems(prev => prev.filter(item => item.id !== id)),
        },
      ]
    );
  };

  const useSuggestion = (suggestion: string) => {
    setInputText(suggestion);
  };

  useEffect(() => {
    scrollViewRef.current?.scrollToEnd({ animated: true });
  }, [messages]);

  return (
    <SafeAreaView style={styles.container}>
      <KeyboardAvoidingView
        style={styles.keyboardView}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}>
        
        {/* Header */}
        <View style={styles.header}>
          <View style={styles.headerLeft}>
            <Bot size={24} color="#4A90E2" strokeWidth={2} />
            <Text style={styles.headerTitle}>AI智能规划师</Text>
          </View>
          <TouchableOpacity
            onPress={() => setShowTodoPanel(!showTodoPanel)}
            style={styles.todoToggle}
            activeOpacity={0.8}>
            <Text style={styles.todoToggleText}>
              行程清单 ({todoItems.length})
            </Text>
            {showTodoPanel ? (
              <ChevronUp size={16} color="#4A90E2" strokeWidth={2} />
            ) : (
              <ChevronDown size={16} color="#4A90E2" strokeWidth={2} />
            )}
          </TouchableOpacity>
        </View>

        {/* Todo Panel */}
        {showTodoPanel && (
          <View style={styles.todoPanel}>
            <View style={styles.todoPanelHeader}>
              <Text style={styles.todoPanelTitle}>我的行程清单</Text>
              <TouchableOpacity onPress={addTodoItem} style={styles.addTodoButton}>
                <Plus size={16} color="#4A90E2" strokeWidth={2} />
              </TouchableOpacity>
            </View>
            <ScrollView style={styles.todoList} showsVerticalScrollIndicator={false}>
              {todoItems.map(item => (
                <View key={item.id} style={styles.todoItem}>
                  <TouchableOpacity
                    onPress={() => toggleTodoItem(item.id)}
                    style={styles.todoCheckbox}>
                    {item.completed ? (
                      <Check size={16} color="#28A745" strokeWidth={2} />
                    ) : (
                      <View style={styles.uncheckedBox} />
                    )}
                  </TouchableOpacity>
                  <View style={styles.todoContent}>
                    <Text style={[
                      styles.todoTitle,
                      item.completed && styles.todoTitleCompleted
                    ]}>
                      {item.title}
                    </Text>
                    <View style={styles.todoMeta}>
                      <Clock size={12} color="#6C757D" strokeWidth={2} />
                      <Text style={styles.todoTime}>{item.time}</Text>
                      <MapPin size={12} color="#6C757D" strokeWidth={2} />
                      <Text style={styles.todoLocation}>{item.location}</Text>
                    </View>
                  </View>
                  <TouchableOpacity
                    onPress={() => deleteTodoItem(item.id)}
                    style={styles.deleteButton}>
                    <X size={16} color="#DC3545" strokeWidth={2} />
                  </TouchableOpacity>
                </View>
              ))}
              {todoItems.length === 0 && (
                <Text style={styles.emptyTodoText}>
                  暂无行程项目，让AI为您规划吧！
                </Text>
              )}
            </ScrollView>
          </View>
        )}

        {/* Chat Messages */}
        <ScrollView
          ref={scrollViewRef}
          style={styles.chatContainer}
          showsVerticalScrollIndicator={false}
          contentContainerStyle={styles.chatContent}>
          {messages.map(message => (
            <View key={message.id} style={styles.messageContainer}>
              <View style={[
                styles.messageBubble,
                message.type === 'user' ? styles.userMessage : styles.aiMessage
              ]}>
                <View style={styles.messageHeader}>
                  {message.type === 'ai' ? (
                    <Bot size={16} color="#4A90E2" strokeWidth={2} />
                  ) : (
                    <User size={16} color="#FFFFFF" strokeWidth={2} />
                  )}
                  <Text style={[
                    styles.messageAuthor,
                    message.type === 'user' && styles.userMessageAuthor
                  ]}>
                    {message.type === 'ai' ? 'AI助手' : '我'}
                  </Text>
                </View>
                <Text style={[
                  styles.messageText,
                  message.type === 'user' && styles.userMessageText
                ]}>
                  {message.content}
                </Text>
                {message.suggestions && (
                  <View style={styles.suggestionsContainer}>
                    {message.suggestions.map((suggestion, index) => (
                      <TouchableOpacity
                        key={index}
                        style={styles.suggestionChip}
                        onPress={() => useSuggestion(suggestion)}
                        activeOpacity={0.8}>
                        <Text style={styles.suggestionText}>{suggestion}</Text>
                      </TouchableOpacity>
                    ))}
                  </View>
                )}
              </View>
            </View>
          ))}
          
          {isTyping && (
            <View style={styles.messageContainer}>
              <View style={[styles.messageBubble, styles.aiMessage]}>
                <View style={styles.messageHeader}>
                  <Bot size={16} color="#4A90E2" strokeWidth={2} />
                  <Text style={styles.messageAuthor}>AI助手</Text>
                </View>
                <View style={styles.typingIndicator}>
                  <View style={styles.typingDot} />
                  <View style={styles.typingDot} />
                  <View style={styles.typingDot} />
                </View>
              </View>
            </View>
          )}
        </ScrollView>

        {/* Input Area */}
        <View style={styles.inputContainer}>
          <View style={styles.inputWrapper}>
            <TextInput
              style={styles.textInput}
              placeholder="告诉我您的旅行想法..."
              value={inputText}
              onChangeText={setInputText}
              multiline
              maxLength={500}
              placeholderTextColor="#BBBBBB"
            />
            <TouchableOpacity
              onPress={sendMessage}
              style={[
                styles.sendButton,
                !inputText.trim() && styles.sendButtonDisabled
              ]}
              disabled={!inputText.trim()}
              activeOpacity={0.8}>
              <Send size={20} color="#FFFFFF" strokeWidth={2} />
            </TouchableOpacity>
          </View>
        </View>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FAFAFA', // 更温和的背景色
  },
  keyboardView: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 24, // 增加水平间距
    paddingVertical: 20, // 增加垂直间距
    backgroundColor: '#FFFFFF',
    borderBottomWidth: 0, // 移除边框，更简洁
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.03, // 极其微妙的阴影
    shadowRadius: 8,
    elevation: 1,
  },
  headerLeft: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 20, // 稍微增大字体
    fontWeight: '400', // 使用常规字重，更优雅
    color: '#1A1A1A', // 更深的黑色，提高对比度
    marginLeft: 12,
    letterSpacing: -0.2, // 微调字间距
  },
  todoToggle: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'transparent', // 移除背景色
    paddingHorizontal: 0,
    paddingVertical: 0,
  },
  todoToggleText: {
    fontSize: 15,
    color: '#666666', // 更柔和的灰色
    fontWeight: '400',
    marginRight: 6,
  },
  todoPanel: {
    backgroundColor: '#FFFFFF',
    borderBottomWidth: 0, // 移除边框
    maxHeight: height * 0.35, // 稍微增加高度
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.04,
    shadowRadius: 12,
    elevation: 2,
  },
  todoPanelHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 24,
    paddingVertical: 16, // 增加垂直间距
    borderBottomWidth: 0, // 移除边框
  },
  todoPanelTitle: {
    fontSize: 18,
    fontWeight: '500',
    color: '#1A1A1A',
    letterSpacing: -0.2,
  },
  addTodoButton: {
    width: 36, // 稍微增大
    height: 36,
    borderRadius: 18,
    backgroundColor: '#F5F5F5', // 更柔和的背景
    justifyContent: 'center',
    alignItems: 'center',
  },
  todoList: {
    paddingHorizontal: 24,
    paddingVertical: 12,
  },
  todoItem: {
    flexDirection: 'row',
    alignItems: 'flex-start', // 改为顶部对齐
    paddingVertical: 16, // 增加间距
    borderBottomWidth: 0, // 移除分割线
    marginBottom: 8, // 添加底部间距
  },
  todoCheckbox: {
    width: 20, // 稍微减小
    height: 20,
    borderRadius: 10,
    backgroundColor: 'transparent',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16, // 增加右边距
    marginTop: 2, // 微调对齐
  },
  uncheckedBox: {
    width: 20,
    height: 20,
    borderRadius: 10,
    borderWidth: 1.5, // 减少边框宽度
    borderColor: '#CCCCCC', // 更柔和的边框色
  },
  todoContent: {
    flex: 1,
  },
  todoTitle: {
    fontSize: 16,
    fontWeight: '400', // 使用常规字重
    color: '#1A1A1A',
    marginBottom: 6, // 增加间距
    lineHeight: 22, // 增加行高
  },
  todoTitleCompleted: {
    textDecorationLine: 'line-through',
    color: '#999999',
  },
  todoMeta: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 2,
  },
  todoTime: {
    fontSize: 13,
    color: '#888888', // 更柔和的灰色
    marginLeft: 4,
    marginRight: 12, // 增加间距
  },
  todoLocation: {
    fontSize: 13,
    color: '#888888',
    marginLeft: 4,
  },
  deleteButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: 'transparent', // 移除背景色，更简洁
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: 2, // 微调对齐
  },
  emptyTodoText: {
    textAlign: 'center',
    color: '#BBBBBB', // 更柔和的提示文字
    fontSize: 15,
    paddingVertical: 32, // 增加间距
    lineHeight: 22,
  },
  chatContainer: {
    flex: 1,
    paddingHorizontal: 24, // 增加水平间距
  },
  chatContent: {
    paddingVertical: 24, // 增加垂直间距
  },
  messageContainer: {
    marginBottom: 24, // 增加消息间距
  },
  messageBubble: {
    maxWidth: width * 0.75, // 稍微减小宽度，更优雅
    padding: 20, // 增加内边距
    borderRadius: 20, // 增加圆角
  },
  userMessage: {
    backgroundColor: '#1A1A1A', // 使用更优雅的深色
    alignSelf: 'flex-end',
    borderBottomRightRadius: 6, // 稍微增加圆角
  },
  aiMessage: {
    backgroundColor: '#FFFFFF',
    alignSelf: 'flex-start',
    borderBottomLeftRadius: 6,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.04, // 更微妙的阴影
    shadowRadius: 12,
    elevation: 2,
    borderWidth: 1,
    borderColor: '#F0F0F0', // 添加微妙边框
  },
  messageHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12, // 增加间距
  },
  messageAuthor: {
    fontSize: 13,
    fontWeight: '500',
    color: '#666666', // 更柔和的颜色
    marginLeft: 8,
    letterSpacing: -0.1,
  },
  userMessageAuthor: {
    color: '#FFFFFF',
    opacity: 0.8, // 添加透明度
  },
  messageText: {
    fontSize: 16, // 稍微增大字体
    lineHeight: 24, // 增加行高，提高可读性
    color: '#1A1A1A',
    letterSpacing: -0.1,
  },
  userMessageText: {
    color: '#FFFFFF',
  },
  suggestionsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginTop: 16, // 增加间距
    gap: 10, // 增加间距
  },
  suggestionChip: {
    backgroundColor: 'transparent', // 移除背景色
    paddingHorizontal: 16, // 增加水平间距
    paddingVertical: 10, // 增加垂直间距
    borderRadius: 20, // 增加圆角
    borderWidth: 1,
    borderColor: '#E0E0E0', // 更柔和的边框色
  },
  suggestionText: {
    fontSize: 14,
    color: '#666666', // 更柔和的文字颜色
    fontWeight: '400', // 使用常规字重
  },
  typingIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 4,
  },
  typingDot: {
    width: 6, // 稍微减小
    height: 6,
    borderRadius: 3,
    backgroundColor: '#CCCCCC', // 更柔和的颜色
    marginRight: 4,
  },
  inputContainer: {
    backgroundColor: '#FFFFFF',
    borderTopWidth: 0, // 移除边框
    paddingHorizontal: 24, // 增加水平间距
    paddingVertical: 20, // 增加垂直间距
    shadowColor: '#000',
    shadowOffset: { width: 0, height: -2 },
    shadowOpacity: 0.03, // 添加微妙的上阴影
    shadowRadius: 12,
    elevation: 3,
  },
  inputWrapper: {
    flexDirection: 'row',
    alignItems: 'flex-end',
    backgroundColor: '#F8F8F8', // 更柔和的背景色
    borderRadius: 28, // 增加圆角
    paddingHorizontal: 20, // 增加水平间距
    paddingVertical: 12, // 增加垂直间距
    minHeight: 56, // 增加最小高度
    borderWidth: 1,
    borderColor: '#F0F0F0', // 添加微妙边框
  },
  textInput: {
    flex: 1,
    fontSize: 16,
    color: '#1A1A1A',
    maxHeight: 120, // 增加最大高度
    paddingVertical: 8,
    lineHeight: 22, // 增加行高
    letterSpacing: -0.1,
  },
  sendButton: {
    width: 40, // 稍微增大
    height: 40,
    borderRadius: 20,
    backgroundColor: '#1A1A1A', // 使用更优雅的深色
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: 12, // 增加间距
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  sendButtonDisabled: {
    backgroundColor: '#DDDDDD', // 更柔和的禁用状态
    shadowOpacity: 0, // 移除阴影
    elevation: 0,
  },
});