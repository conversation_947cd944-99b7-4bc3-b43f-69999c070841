import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Image,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { MapPin, Calendar, Clock, Users } from 'lucide-react-native';
import { useRouter } from 'expo-router';

interface Trip {
  id: string;
  destination: string;
  startDate: string;
  endDate: string;
  image: string;
  status: 'upcoming' | 'completed' | 'draft';
  travelers: number;
}

export default function TripsScreen() {
  const router = useRouter();

  const trips: Trip[] = [
    {
      id: '1',
      destination: '东京，日本',
      startDate: '3月15日',
      endDate: '3月22日',
      image: 'https://images.pexels.com/photos/2506923/pexels-photo-2506923.jpeg?auto=compress&cs=tinysrgb&w=400',
      status: 'upcoming',
      travelers: 2,
    },
    {
      id: '2',
      destination: '巴黎，法国',
      startDate: '1月10日',
      endDate: '1月17日',
      image: 'https://images.pexels.com/photos/338515/pexels-photo-338515.jpeg?auto=compress&cs=tinysrgb&w=400',
      status: 'completed',
      travelers: 1,
    },
    {
      id: '3',
      destination: '巴厘岛，印度尼西亚',
      startDate: '5月5日',
      endDate: '5月12日',
      image: 'https://images.pexels.com/photos/1374064/pexels-photo-1374064.jpeg?auto=compress&cs=tinysrgb&w=400',
      status: 'draft',
      travelers: 4,
    },
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'upcoming':
        return '#4A90E2';
      case 'completed':
        return '#28A745';
      case 'draft':
        return '#FFC107';
      default:
        return '#6C757D';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'upcoming':
        return '即将开始';
      case 'completed':
        return '已完成';
      case 'draft':
        return '草稿';
      default:
        return status;
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <Text style={styles.headerTitle}>我的行程</Text>
        <TouchableOpacity
          onPress={() => router.push('/create')}
          style={styles.addButton}
          activeOpacity={0.8}>
          <Text style={styles.addButtonText}>+ 新建行程</Text>
        </TouchableOpacity>
      </View>

      {/* Trips List */}
      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {trips.map(trip => (
          <TouchableOpacity
            key={trip.id}
            style={styles.tripCard}
            onPress={() =>
              trip.status === 'upcoming' ? router.push('/daily-view') : null
            }
            activeOpacity={0.95}>
            <Image source={{ uri: trip.image }} style={styles.tripImage} />
            <View style={styles.tripInfo}>
              <View style={styles.tripHeader}>
                <Text style={styles.destination}>{trip.destination}</Text>
                <View
                  style={[
                    styles.statusBadge,
                    { backgroundColor: getStatusColor(trip.status) },
                  ]}>
                  <Text style={styles.statusText}>
                    {getStatusText(trip.status)}
                  </Text>
                </View>
              </View>

              <View style={styles.tripDetails}>
                <View style={styles.detailRow}>
                  <Calendar size={16} color="#6C757D" strokeWidth={2} />
                  <Text style={styles.detailText}>
                    {trip.startDate} - {trip.endDate}
                  </Text>
                </View>
                <View style={styles.detailRow}>
                  <Users size={16} color="#6C757D" strokeWidth={2} />
                  <Text style={styles.detailText}>
                    {trip.travelers} 位旅行者
                  </Text>
                </View>
              </View>

              {trip.status === 'upcoming' && (
                <View style={styles.countdownRow}>
                  <Clock size={16} color="#4A90E2" strokeWidth={2} />
                  <Text style={styles.countdownText}>还有12天</Text>
                </View>
              )}
            </View>
          </TouchableOpacity>
        ))}
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F8F9FA',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 20,
    backgroundColor: '#FFFFFF',
    borderBottomWidth: 1,
    borderBottomColor: '#E9ECEF',
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: '700',
    color: '#212529',
  },
  addButton: {
    backgroundColor: '#4A90E2',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 8,
  },
  addButtonText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#FFFFFF',
  },
  content: {
    flex: 1,
    paddingHorizontal: 20,
    paddingTop: 20,
  },
  tripCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 16,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 8,
    elevation: 2,
    overflow: 'hidden',
  },
  tripImage: {
    width: '100%',
    height: 140,
    resizeMode: 'cover',
  },
  tripInfo: {
    padding: 16,
  },
  tripHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  destination: {
    fontSize: 18,
    fontWeight: '600',
    color: '#212529',
    flex: 1,
  },
  statusBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 6,
  },
  statusText: {
    fontSize: 12,
    fontWeight: '600',
    color: '#FFFFFF',
  },
  tripDetails: {
    marginBottom: 12,
  },
  detailRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 6,
  },
  detailText: {
    fontSize: 14,
    color: '#6C757D',
    marginLeft: 8,
    fontWeight: '500',
  },
  countdownRow: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#F8F9FA',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 8,
    alignSelf: 'flex-start',
  },
  countdownText: {
    fontSize: 14,
    color: '#4A90E2',
    marginLeft: 6,
    fontWeight: '600',
  },
});